{% extends 'App/base.html' %}

{% block title %}Home - Employee Leave Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-light p-5 rounded">
            <h1 class="display-4">Welcome to Employee Leave Management System</h1>
            <p class="lead">Manage employees, leave requests, and approvals efficiently.</p>
            <hr class="my-4">
            <p>Use the navigation menu to access different sections of the application.</p>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x text-primary mb-3"></i>
                <h5 class="card-title">Employees</h5>
                <p class="card-text">Manage employee information and departments.</p>
                <a href="{% url 'employee_list' %}" class="btn btn-primary">View Employees</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-3x text-success mb-3"></i>
                <h5 class="card-title">Leave Requests</h5>
                <p class="card-text">Create and manage employee leave requests.</p>
                <a href="{% url 'leave_request_list' %}" class="btn btn-success">View Requests</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-3x text-warning mb-3"></i>
                <h5 class="card-title">Approvals</h5>
                <p class="card-text">Review and approve leave requests.</p>
                <a href="{% url 'approval_list' %}" class="btn btn-warning">View Approvals</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
