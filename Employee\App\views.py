from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from .models import Employee, LeaveRequest, Approval
from .forms import EmployeeForm, LeaveRequestForm, ApprovalForm


def home(request):
    return render(request, 'App/home.html')


def employee_list(request):
    employees = Employee.objects.all()
    return render(request, 'App/employee_list.html', {'employees': employees})

def employee_create(request):
    if request.method == 'POST':
        form = EmployeeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Employee created successfully!')
            return redirect('employee_list')
    else:
        form = EmployeeForm()
    return render(request, 'App/employee_form.html', {'form': form, 'title': 'Add Employee'})

def employee_update(request, pk):
    employee = get_object_or_404(Employee, pk=pk)
    if request.method == 'POST':
        form = EmployeeForm(request.POST, instance=employee)
        if form.is_valid():
            form.save()
            messages.success(request, 'Employee updated successfully!')
            return redirect('employee_list')
    else:
        form = EmployeeForm(instance=employee)
    return render(request, 'App/employee_form.html', {'form': form, 'title': 'Update Employee'})

def employee_delete(request, pk):
    employee = get_object_or_404(Employee, pk=pk)
    if request.method == 'POST':
        employee.delete()
        messages.success(request, 'Employee deleted successfully!')
        return redirect('employee_list')
    return render(request, 'App/employee_confirm_delete.html', {'employee': employee})


def leave_request_list(request):
    leave_requests = LeaveRequest.objects.all().select_related('employee')
    return render(request, 'App/leave_request_list.html', {'leave_requests': leave_requests})

def leave_request_create(request):
    if request.method == 'POST':
        form = LeaveRequestForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Leave request created successfully!')
            return redirect('leave_request_list')
    else:
        form = LeaveRequestForm()
    return render(request, 'App/leave_request_form.html', {'form': form, 'title': 'Create Leave Request'})

def leave_request_update(request, pk):
    leave_request = get_object_or_404(LeaveRequest, pk=pk)
    if request.method == 'POST':
        form = LeaveRequestForm(request.POST, instance=leave_request)
        if form.is_valid():
            form.save()
            messages.success(request, 'Leave request updated successfully!')
            return redirect('leave_request_list')
    else:
        form = LeaveRequestForm(instance=leave_request)
    return render(request, 'App/leave_request_form.html', {'form': form, 'title': 'Update Leave Request'})

def leave_request_delete(request, pk):
    leave_request = get_object_or_404(LeaveRequest, pk=pk)
    if request.method == 'POST':
        leave_request.delete()
        messages.success(request, 'Leave request deleted successfully!')
        return redirect('leave_request_list')
    return render(request, 'App/leave_request_confirm_delete.html', {'leave_request': leave_request})


def approval_list(request):
    approvals = Approval.objects.all().select_related('request__employee')
    return render(request, 'App/approval_list.html', {'approvals': approvals})

def approval_create(request):
    if request.method == 'POST':
        form = ApprovalForm(request.POST)
        if form.is_valid():
            approval = form.save()
            leave_request = approval.request
            leave_request.status = approval.decision
            leave_request.save()
            messages.success(request, 'Approval created successfully!')
            return redirect('approval_list')
    else:
        form = ApprovalForm()
    return render(request, 'App/approval_form.html', {'form': form, 'title': 'Create Approval'})

def approval_update(request, pk):
    approval = get_object_or_404(Approval, pk=pk)
    if request.method == 'POST':
        form = ApprovalForm(request.POST, instance=approval)
        if form.is_valid():
            approval = form.save()
            
            leave_request = approval.request
            leave_request.status = approval.decision
            leave_request.save()
            messages.success(request, 'Approval updated successfully!')
            return redirect('approval_list')
    else:
        form = ApprovalForm(instance=approval)
    return render(request, 'App/approval_form.html', {'form': form, 'title': 'Update Approval'})

def approval_delete(request, pk):
    approval = get_object_or_404(Approval, pk=pk)
    if request.method == 'POST':
        approval.delete()
        messages.success(request, 'Approval deleted successfully!')
        return redirect('approval_list')
    return render(request, 'App/approval_confirm_delete.html', {'approval': approval})
