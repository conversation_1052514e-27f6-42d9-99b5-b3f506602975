from django.contrib import admin
from .models import Employee, LeaveRequest, Approval

@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['name', 'department']
    list_filter = ['department']
    search_fields = ['name', 'department']

@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    list_display = ['employee', 'start_date', 'end_date', 'status']
    list_filter = ['status', 'start_date', 'employee__department']
    search_fields = ['employee__name']
    date_hierarchy = 'start_date'

@admin.register(Approval)
class ApprovalAdmin(admin.ModelAdmin):
    list_display = ['approver', 'request', 'decision']
    list_filter = ['decision']
    search_fields = ['approver', 'request__employee__name']
