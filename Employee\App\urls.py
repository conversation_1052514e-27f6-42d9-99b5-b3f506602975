from django.urls import path
from . import views

urlpatterns = [
    # Home
    path('', views.home, name='home'),
    
    # Employee URLs
    path('employees/', views.employee_list, name='employee_list'),
    path('employees/create/', views.employee_create, name='employee_create'),
    path('employees/<int:pk>/update/', views.employee_update, name='employee_update'),
    path('employees/<int:pk>/delete/', views.employee_delete, name='employee_delete'),
    
    # Leave Request URLs
    path('leave-requests/', views.leave_request_list, name='leave_request_list'),
    path('leave-requests/create/', views.leave_request_create, name='leave_request_create'),
    path('leave-requests/<int:pk>/update/', views.leave_request_update, name='leave_request_update'),
    path('leave-requests/<int:pk>/delete/', views.leave_request_delete, name='leave_request_delete'),
    
    # Approval URLs
    path('approvals/', views.approval_list, name='approval_list'),
    path('approvals/create/', views.approval_create, name='approval_create'),
    path('approvals/<int:pk>/update/', views.approval_update, name='approval_update'),
    path('approvals/<int:pk>/delete/', views.approval_delete, name='approval_delete'),
]
