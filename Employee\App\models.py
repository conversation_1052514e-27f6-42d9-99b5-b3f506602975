from django.db import models
class Employee(models.Model):
    name = models.Char<PERSON>ield(max_length=100)
    department = models.Char<PERSON>ield(max_length=100)

    def __str__(self):
        return self.name
class Employee(models.Model):
    LeaveRequest = models.Char<PERSON>ield(max_length=100)

    def __str__(self):
        return self.name
class Employee(models.Model):
    Approval = models.Char<PERSON>ield(max_length=100)

    def __str__(self):
        return self.name

# Create your models here.
