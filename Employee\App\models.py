from django.db import models

class Employee(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100)
    department = models.Char<PERSON><PERSON>(max_length=100)

    def __str__(self):
        return self.name

class LeaveRequest(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    def __str__(self):
        return f"{self.employee.name} - {self.start_date} to {self.end_date}"

class Approval(models.Model):
    DECISION_CHOICES = [
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    approver = models.Char<PERSON>ield(max_length=100)
    request = models.Foreign<PERSON>ey(LeaveRequest, on_delete=models.CASCADE)
    decision = models.CharField(max_length=20, choices=DECISION_CHOICES)

    def __str__(self):
        return f"{self.approver} - {self.decision} for {self.request}"
