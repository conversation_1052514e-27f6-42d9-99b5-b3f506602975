{% extends 'App/base.html' %}

{% block title %}Delete Employee - Employee Leave Management{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4><i class="fas fa-exclamation-triangle"></i> Confirm Delete</h4>
            </div>
            <div class="card-body">
                <p>Are you sure you want to delete the employee <strong>{{ employee.name }}</strong> from <strong>{{ employee.department }}</strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i> This action cannot be undone.
                </div>
                <form method="post">
                    {% csrf_token %}
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'employee_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
