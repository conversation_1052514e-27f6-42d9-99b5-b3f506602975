from django import forms
from .models import Employee, LeaveRequest, Approval

class EmployeeForm(forms.ModelForm):
    class Meta:
        model = Employee
        fields = ['name', 'department']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter employee name'}),
            'department': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter department'}),
        }

class LeaveRequestForm(forms.ModelForm):
    class Meta:
        model = LeaveRequest
        fields = ['employee', 'start_date', 'end_date', 'status']
        widgets = {
            'employee': forms.Select(attrs={'class': 'form-control'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if start_date > end_date:
                raise forms.ValidationError("Start date cannot be after end date.")
        
        return cleaned_data

class ApprovalForm(forms.ModelForm):
    class Meta:
        model = Approval
        fields = ['approver', 'request', 'decision']
        widgets = {
            'approver': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter approver name'}),
            'request': forms.Select(attrs={'class': 'form-control'}),
            'decision': forms.Select(attrs={'class': 'form-control'}),
        }
