{% extends 'App/base.html' %}

{% block title %}Leave Requests - Employee Leave Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-calendar-alt"></i> Leave Requests</h2>
    <a href="{% url 'leave_request_create' %}" class="btn btn-success">
        <i class="fas fa-plus"></i> Create Leave Request
    </a>
</div>

{% if leave_requests %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Employee</th>
                        <th>Department</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in leave_requests %}
                    <tr>
                        <td>{{ request.id }}</td>
                        <td>{{ request.employee.name }}</td>
                        <td>{{ request.employee.department }}</td>
                        <td>{{ request.start_date }}</td>
                        <td>{{ request.end_date }}</td>
                        <td>
                            {% if request.status == 'pending' %}
                                <span class="badge bg-warning">{{ request.get_status_display }}</span>
                            {% elif request.status == 'approved' %}
                                <span class="badge bg-success">{{ request.get_status_display }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ request.get_status_display }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'leave_request_update' request.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="{% url 'leave_request_delete' request.pk %}" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle"></i> No leave requests found. <a href="{% url 'leave_request_create' %}">Create the first leave request</a>.
</div>
{% endif %}
{% endblock %}
