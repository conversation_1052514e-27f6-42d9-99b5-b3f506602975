{% extends 'App/base.html' %}

{% block title %}Employees - Employee Leave Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users"></i> Employees</h2>
    <a href="{% url 'employee_create' %}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add Employee
    </a>
</div>

{% if employees %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Department</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>{{ employee.id }}</td>
                        <td>{{ employee.name }}</td>
                        <td>{{ employee.department }}</td>
                        <td>
                            <a href="{% url 'employee_update' employee.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="{% url 'employee_delete' employee.pk %}" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle"></i> No employees found. <a href="{% url 'employee_create' %}">Add the first employee</a>.
</div>
{% endif %}
{% endblock %}
